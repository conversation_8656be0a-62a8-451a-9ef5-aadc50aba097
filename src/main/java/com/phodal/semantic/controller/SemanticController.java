package com.phodal.semantic.controller;

import com.phodal.semantic.PlannerService;
import com.phodal.semantic.config.ConfigurationManager;
import com.phodal.semantic.model.DynamicModelService;
import com.phodal.semantic.tools.ToolLoader;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Semantic Migration 框架的 REST API 控制器
 */
@RestController
@RequestMapping("/api/semantic")
public class SemanticController {
    
    private final PlannerService plannerService;
    private final DynamicModelService dynamicModelService;
    private final ToolLoader toolLoader;
    private final ConfigurationManager configurationManager;
    
    public SemanticController(PlannerService plannerService, 
                            DynamicModelService dynamicModelService,
                            ToolLoader toolLoader,
                            ConfigurationManager configurationManager) {
        this.plannerService = plannerService;
        this.dynamicModelService = dynamicModelService;
        this.toolLoader = toolLoader;
        this.configurationManager = configurationManager;
    }
    
    /**
     * 执行规划请求
     */
    @PostMapping("/plan")
    public Map<String, Object> plan(@RequestBody Map<String, String> request) {
        String input = request.get("input");
        String businessType = request.getOrDefault("businessType", "plan");
        
        Map<String, Object> response = new HashMap<>();
        try {
            String result = plannerService.plan(input, businessType);
            response.put("success", true);
            response.put("result", result);
            response.put("businessType", businessType);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 使用自定义提示词执行规划
     */
    @PostMapping("/plan/custom")
    public Map<String, Object> planWithCustomPrompt(@RequestBody Map<String, String> request) {
        String input = request.get("input");
        String businessType = request.getOrDefault("businessType", "plan");
        String systemPrompt = request.get("systemPrompt");
        
        Map<String, Object> response = new HashMap<>();
        try {
            String result = plannerService.planWithCustomPrompt(input, businessType, systemPrompt);
            response.put("success", true);
            response.put("result", result);
            response.put("businessType", businessType);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 获取可用工具列表
     */
    @GetMapping("/tools")
    public Map<String, Object> getAvailableTools(@RequestParam(defaultValue = "plan") String businessType) {
        Map<String, Object> response = new HashMap<>();
        try {
            String toolsInfo = plannerService.getAvailableTools(businessType);
            response.put("success", true);
            response.put("tools", toolsInfo);
            response.put("businessType", businessType);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 获取系统状态
     */
    @GetMapping("/status")
    public Map<String, Object> getSystemStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // 配置状态
        status.put("configurations", configurationManager.getConfigurationStats());
        status.put("configValidation", configurationManager.validateConfigurations());
        
        // 模型服务状态
        status.put("modelService", Map.of(
            "cacheSize", dynamicModelService.getCacheSize(),
            "defaultClientAvailable", dynamicModelService.getDefaultChatClient() != null
        ));
        
        // 工具加载器状态
        status.put("toolLoader", Map.of(
            "cacheSize", toolLoader.getCacheSize(),
            "generalToolsCount", toolLoader.getGeneralTools().size()
        ));
        
        return status;
    }
    
    /**
     * 重新加载配置
     */
    @PostMapping("/reload")
    public Map<String, Object> reloadConfigurations(@RequestParam(required = false) String businessType) {
        Map<String, Object> response = new HashMap<>();
        try {
            if (businessType != null && !businessType.trim().isEmpty()) {
                configurationManager.reloadBusinessConfiguration(businessType);
                response.put("message", "Business configuration reloaded for: " + businessType);
            } else {
                configurationManager.reloadConfigurations();
                dynamicModelService.clearCache();
                toolLoader.clearCache();
                plannerService.reloadPromptTemplate();
                response.put("message", "All configurations reloaded");
            }
            response.put("success", true);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        boolean isHealthy = true;
        StringBuilder issues = new StringBuilder();
        
        // 检查配置管理器
        if (configurationManager.getLlmConfig() == null) {
            isHealthy = false;
            issues.append("LLM configuration missing; ");
        }
        
        // 检查模型服务
        if (dynamicModelService.getDefaultChatClient() == null) {
            isHealthy = false;
            issues.append("Default ChatClient not available; ");
        }
        
        // 检查工具加载器
        if (toolLoader.getGeneralTools().isEmpty()) {
            isHealthy = false;
            issues.append("No general tools loaded; ");
        }
        
        health.put("healthy", isHealthy);
        health.put("status", isHealthy ? "UP" : "DOWN");
        if (!isHealthy) {
            health.put("issues", issues.toString());
        }
        
        return health;
    }
    
    /**
     * 获取业务配置信息
     */
    @GetMapping("/config/{businessType}")
    public Map<String, Object> getBusinessConfig(@PathVariable String businessType) {
        Map<String, Object> response = new HashMap<>();
        
        var businessConfig = configurationManager.getBusinessConfig(businessType);
        var promptTemplate = configurationManager.getPromptTemplate(businessType);
        
        response.put("businessType", businessType);
        response.put("config", businessConfig);
        response.put("promptTemplate", promptTemplate);
        response.put("hasConfig", businessConfig != null);
        response.put("hasPromptTemplate", promptTemplate != null);
        
        return response;
    }
}
