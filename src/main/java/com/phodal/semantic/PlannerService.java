package com.phodal.semantic;

import com.phodal.semantic.model.DynamicModelService;
import com.phodal.semantic.tools.ToolLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 规划服务，负责处理用户请求并生成相应的执行计划
 */
@Service
public class PlannerService {

    private static final Logger logger = LoggerFactory.getLogger(PlannerService.class);
    private final DynamicModelService dynamicModelService;
    private final ToolLoader toolLoader;
    private String planPromptTemplate;

    public PlannerService(DynamicModelService dynamicModelService, ToolLoader toolLoader) {
        this.dynamicModelService = dynamicModelService;
        this.toolLoader = toolLoader;
        loadPlanPromptTemplate();
    }

    /**
     * 加载规划提示词模板
     */
    private void loadPlanPromptTemplate() {
        try {
            ClassPathResource resource = new ClassPathResource("plan/skprompt.txt");
            this.planPromptTemplate = resource.getContentAsString(StandardCharsets.UTF_8);
            logger.info("Plan prompt template loaded successfully");
        } catch (IOException e) {
            logger.error("Failed to load plan prompt template", e);
            // 使用默认模板
            this.planPromptTemplate = "你是一个智能助手，请根据用户的需求选择合适的工具来回答问题。";
        }
    }

    /**
     * 根据用户输入和业务类型生成执行计划
     * @param input 用户输入
     * @param businessType 业务类型
     * @return 执行计划结果
     */
    public String plan(String input, String businessType) {
        if (input == null || input.trim().isEmpty()) {
            return "请提供有效的输入内容。";
        }

        try {
            logger.info("Processing plan request for business: {}, input: {}", businessType, input);

            // 获取对应业务的 ChatClient
            ChatClient chatClient = dynamicModelService.getChatClient(businessType);
            if (chatClient == null) {
                chatClient = dynamicModelService.getDefaultChatClient();
            }

            if (chatClient == null) {
                logger.error("No ChatClient available");
                return "系统暂时不可用，请稍后重试。";
            }

            // 获取可用的工具回调
            List<ToolCallback> availableTools = toolLoader.getAllAvailableTools(businessType);
            logger.info("Loaded {} tools for business: {}", availableTools.size(), businessType);

            // 调用 ChatClient 生成响应
            String response = chatClient.prompt()
                    .system(planPromptTemplate)
                    .user(input)
                    .toolCallbacks(availableTools)
                    .call()
                    .content();

            logger.info("Plan generated successfully for business: {}", businessType);
            return response;

        } catch (Exception e) {
            logger.error("Failed to generate plan for input: {}", input, e);
            return "生成计划时发生错误：" + e.getMessage();
        }
    }



    /**
     * 处理特定业务的规划请求
     * @param input 用户输入
     * @param businessType 业务类型
     * @param systemPrompt 自定义系统提示词
     * @return 执行计划结果
     */
    public String planWithCustomPrompt(String input, String businessType, String systemPrompt) {
        if (input == null || input.trim().isEmpty()) {
            return "请提供有效的输入内容。";
        }

        try {
            logger.info("Processing custom plan request for business: {}", businessType);

            // 获取对应业务的 ChatClient
            ChatClient chatClient = dynamicModelService.getChatClient(businessType);
            if (chatClient == null) {
                chatClient = dynamicModelService.getDefaultChatClient();
            }

            if (chatClient == null) {
                logger.error("No ChatClient available");
                return "系统暂时不可用，请稍后重试。";
            }

            // 获取可用的工具回调
            List<ToolCallback> availableTools = toolLoader.getAllAvailableTools(businessType);

            // 使用自定义系统提示词
            String effectiveSystemPrompt = systemPrompt != null ? systemPrompt : planPromptTemplate;

            // 调用 ChatClient 生成响应
            String response = chatClient.prompt()
                    .system(effectiveSystemPrompt)
                    .user(input)
                    .toolCallbacks(availableTools)
                    .call()
                    .content();

            logger.info("Custom plan generated successfully for business: {}", businessType);
            return response;

        } catch (Exception e) {
            logger.error("Failed to generate custom plan for input: {}", input, e);
            return "生成计划时发生错误：" + e.getMessage();
        }
    }

    /**
     * 获取可用的工具列表信息
     * @param businessType 业务类型
     * @return 工具列表描述
     */
    public String getAvailableTools(String businessType) {
        try {
            List<ToolCallback> tools = toolLoader.getAllAvailableTools(businessType);
            StringBuilder toolsInfo = new StringBuilder();
            toolsInfo.append("可用工具列表（业务类型：").append(businessType).append("）：\n\n");

            int index = 1;
            for (ToolCallback tool : tools) {
                toolsInfo.append(index++).append(". ");
                toolsInfo.append(tool.getToolDefinition().name()).append("\n");
                toolsInfo.append("   描述：").append(tool.getToolDefinition().description()).append("\n\n");
            }

            return toolsInfo.toString();
        } catch (Exception e) {
            logger.error("Failed to get available tools for business: {}", businessType, e);
            return "获取工具列表时发生错误：" + e.getMessage();
        }
    }

    /**
     * 重新加载提示词模板
     */
    public void reloadPromptTemplate() {
        loadPlanPromptTemplate();
        logger.info("Prompt template reloaded");
    }
}
