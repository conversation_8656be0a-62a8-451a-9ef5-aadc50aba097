package com.phodal.semantic.tools;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 工具加载器，负责动态加载和管理工具函数
 * 简化版本，直接返回工具实例而不是 ToolCallback
 */
@Component
public class ToolLoader {

    private static final Logger logger = LoggerFactory.getLogger(ToolLoader.class);
    private final Map<String, List<Object>> businessToolsCache = new ConcurrentHashMap<>();
    private final List<Object> generalTools = new ArrayList<>();

    public ToolLoader() {
        initializeGeneralTools();
    }
    
    /**
     * 初始化通用工具
     */
    private void initializeGeneralTools() {
        try {
            // 添加通用工具
            generalTools.add(new GeneralTools());

            logger.info("Initialized {} general tools", generalTools.size());
        } catch (Exception e) {
            logger.error("Failed to initialize general tools", e);
        }
    }

    /**
     * 获取指定业务的工具列表
     * @param businessName 业务名称
     * @return 工具实例列表
     */
    public List<Object> getToolsForBusiness(String businessName) {
        return businessToolsCache.computeIfAbsent(businessName, this::loadBusinessTools);
    }

    /**
     * 获取所有通用工具
     * @return 通用工具列表
     */
    public List<Object> getGeneralTools() {
        return new ArrayList<>(generalTools);
    }

    /**
     * 获取所有可用工具（通用工具 + 业务特定工具）
     * @param businessName 业务名称
     * @return 所有可用工具列表
     */
    public List<Object> getAllAvailableTools(String businessName) {
        List<Object> allTools = new ArrayList<>(generalTools);
        allTools.addAll(getToolsForBusiness(businessName));
        return allTools;
    }
    
    /**
     * 为特定业务加载工具
     */
    private List<Object> loadBusinessTools(String businessName) {
        List<Object> businessTools = new ArrayList<>();

        try {
            switch (businessName.toLowerCase()) {
                case "qa":
                case "tuoguan":
                    // 加载问答相关工具
                    businessTools.add(new QATools());
                    break;

                case "compliance_review":
                    // 加载合规审核工具
                    businessTools.add(new ComplianceTools());
                    break;

                case "plan":
                    // 加载规划工具
                    businessTools.add(new PlanningTools());
                    break;

                default:
                    logger.info("No specific tools found for business: {}", businessName);
                    break;
            }

            logger.info("Loaded {} tools for business: {}", businessTools.size(), businessName);

        } catch (Exception e) {
            logger.error("Failed to load tools for business: {}", businessName, e);
        }

        return businessTools;
    }
    
    /**
     * 清除工具缓存
     */
    public void clearCache() {
        businessToolsCache.clear();
        logger.info("Tool cache cleared");
    }
    
    /**
     * 获取缓存的业务工具数量
     */
    public int getCacheSize() {
        return businessToolsCache.size();
    }
    
    /**
     * 注册自定义工具
     * @param businessName 业务名称
     * @param toolInstance 工具实例
     */
    public void registerCustomTool(String businessName, Object toolInstance) {
        try {
            List<ToolCallback> customTools = createToolCallbacks(toolInstance);
            List<ToolCallback> businessTools = businessToolsCache.computeIfAbsent(businessName, k -> new ArrayList<>());
            businessTools.addAll(customTools);

            logger.info("Registered {} custom tools for business: {}", customTools.size(), businessName);
        } catch (Exception e) {
            logger.error("Failed to register custom tool for business: {}", businessName, e);
        }
    }

    /**
     * 从工具实例创建 ToolCallback 列表
     */
    private List<ToolCallback> createToolCallbacks(Object toolInstance) {
        List<ToolCallback> callbacks = new ArrayList<>();

        // 获取所有标记了 @Tool 注解的方法
        Method[] methods = toolInstance.getClass().getDeclaredMethods();
        for (Method method : methods) {
            if (method.isAnnotationPresent(org.springframework.ai.tool.annotation.Tool.class)) {
                try {
                    org.springframework.ai.tool.annotation.Tool toolAnnotation =
                        method.getAnnotation(org.springframework.ai.tool.annotation.Tool.class);

                    String toolName = toolAnnotation.name().isEmpty() ? method.getName() : toolAnnotation.name();
                    String description = toolAnnotation.description().isEmpty() ? method.getName() : toolAnnotation.description();

                    // 创建 ToolDefinition
                    ToolDefinition toolDefinition = ToolDefinition.builder()
                        .name(toolName)
                        .description(description)
                        .inputSchema("{\"type\":\"object\",\"properties\":{}}")  // 简化的 schema
                        .build();

                    // 创建 MethodToolCallback
                    ToolCallback callback = MethodToolCallback.builder()
                        .toolDefinition(toolDefinition)
                        .toolMethod(method)
                        .toolObject(toolInstance)
                        .build();

                    callbacks.add(callback);

                } catch (Exception e) {
                    logger.warn("Failed to create tool callback for method: {}", method.getName(), e);
                }
            }
        }

        return callbacks;
    }
}
