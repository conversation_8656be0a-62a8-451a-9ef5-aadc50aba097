package com.phodal.semantic.tools;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;

/**
 * 合规审核相关工具类
 */
public class ComplianceTools {
    
    @Tool(description = "对文档内容进行合规性审核")
    public String complianceReview(
            @ToolParam(description = "待审核的文本内容") String reviewContent,
            @ToolParam(description = "用于审核文本的审核点") String reviewPoint) {
        
        if (reviewContent == null || reviewContent.trim().isEmpty()) {
            return "请提供需要审核的文档内容。";
        }
        
        if (reviewPoint == null || reviewPoint.trim().isEmpty()) {
            return "请提供审核要点。";
        }
        
        // 模拟合规审核逻辑
        StringBuilder result = new StringBuilder();
        result.append("合规审核报告\n");
        result.append("==================\n\n");
        result.append("审核内容长度：").append(reviewContent.length()).append(" 字符\n");
        result.append("审核要点：").append(reviewPoint).append("\n\n");
        
        // 简单的合规检查
        boolean hasRiskWords = containsRiskWords(reviewContent);
        boolean hasRequiredElements = containsRequiredElements(reviewContent, reviewPoint);
        
        result.append("审核结果：\n");
        if (hasRiskWords) {
            result.append("⚠️ 发现潜在风险词汇，建议进一步审查\n");
        } else {
            result.append("✅ 未发现明显风险词汇\n");
        }
        
        if (hasRequiredElements) {
            result.append("✅ 包含必要的合规要素\n");
        } else {
            result.append("⚠️ 缺少部分必要的合规要素\n");
        }
        
        result.append("\n建议：\n");
        result.append("1. 确保所有声明符合相关法规要求\n");
        result.append("2. 检查是否包含必要的风险提示\n");
        result.append("3. 验证信息披露的完整性和准确性\n");
        
        return result.toString();
    }
    
    @Tool(description = "检查文档是否符合特定的合规标准")
    public String complianceCheck(
            @ToolParam(description = "文档内容") String content,
            @ToolParam(description = "合规标准名称") String standard) {
        
        if (content == null || content.trim().isEmpty()) {
            return "请提供需要检查的文档内容。";
        }
        
        if (standard == null || standard.trim().isEmpty()) {
            return "请指定合规标准。";
        }
        
        StringBuilder result = new StringBuilder();
        result.append(String.format("合规标准检查报告（标准：%s）\n", standard));
        result.append("==================\n\n");
        
        // 模拟不同标准的检查
        switch (standard.toLowerCase()) {
            case "金融":
            case "finance":
                result.append(checkFinanceCompliance(content));
                break;
            case "数据保护":
            case "data_protection":
                result.append(checkDataProtectionCompliance(content));
                break;
            case "广告":
            case "advertising":
                result.append(checkAdvertisingCompliance(content));
                break;
            default:
                result.append(checkGeneralCompliance(content));
                break;
        }
        
        return result.toString();
    }
    
    @Tool(description = "生成合规性检查清单")
    public String generateComplianceChecklist(@ToolParam(description = "业务类型或行业") String businessType) {
        if (businessType == null || businessType.trim().isEmpty()) {
            return "请提供业务类型或行业信息。";
        }
        
        StringBuilder checklist = new StringBuilder();
        checklist.append(String.format("%s行业合规检查清单\n", businessType));
        checklist.append("==================\n\n");
        
        checklist.append("基础合规要求：\n");
        checklist.append("□ 法律法规遵循性检查\n");
        checklist.append("□ 行业标准符合性验证\n");
        checklist.append("□ 风险提示完整性检查\n");
        checklist.append("□ 信息披露准确性验证\n\n");
        
        checklist.append("文档质量检查：\n");
        checklist.append("□ 语言表达规范性\n");
        checklist.append("□ 术语使用准确性\n");
        checklist.append("□ 格式标准化检查\n");
        checklist.append("□ 版本控制合规性\n\n");
        
        checklist.append("特定行业要求：\n");
        switch (businessType.toLowerCase()) {
            case "金融":
                checklist.append("□ 金融产品风险等级标识\n");
                checklist.append("□ 投资者适当性提示\n");
                checklist.append("□ 收益风险平衡说明\n");
                break;
            case "医疗":
                checklist.append("□ 医疗器械安全说明\n");
                checklist.append("□ 药品使用指导\n");
                checklist.append("□ 副作用风险提示\n");
                break;
            default:
                checklist.append("□ 行业特定要求检查\n");
                checklist.append("□ 专业标准符合性\n");
                break;
        }
        
        return checklist.toString();
    }
    
    private boolean containsRiskWords(String content) {
        String[] riskWords = {"保证", "承诺", "无风险", "稳赚", "必赚"};
        String lowerContent = content.toLowerCase();
        for (String word : riskWords) {
            if (lowerContent.contains(word.toLowerCase())) {
                return true;
            }
        }
        return false;
    }
    
    private boolean containsRequiredElements(String content, String reviewPoint) {
        // 简单检查是否包含审核要点相关内容
        return content.toLowerCase().contains(reviewPoint.toLowerCase());
    }
    
    private String checkFinanceCompliance(String content) {
        StringBuilder result = new StringBuilder();
        result.append("金融合规检查结果：\n");
        result.append("- 风险提示：").append(content.contains("风险") ? "✅ 已包含" : "❌ 缺失").append("\n");
        result.append("- 收益说明：").append(content.contains("收益") ? "✅ 已包含" : "❌ 缺失").append("\n");
        result.append("- 投资建议：").append(content.contains("投资") ? "✅ 已包含" : "❌ 缺失").append("\n");
        return result.toString();
    }
    
    private String checkDataProtectionCompliance(String content) {
        StringBuilder result = new StringBuilder();
        result.append("数据保护合规检查结果：\n");
        result.append("- 隐私声明：").append(content.contains("隐私") ? "✅ 已包含" : "❌ 缺失").append("\n");
        result.append("- 数据使用：").append(content.contains("数据") ? "✅ 已包含" : "❌ 缺失").append("\n");
        result.append("- 用户同意：").append(content.contains("同意") ? "✅ 已包含" : "❌ 缺失").append("\n");
        return result.toString();
    }
    
    private String checkAdvertisingCompliance(String content) {
        StringBuilder result = new StringBuilder();
        result.append("广告合规检查结果：\n");
        result.append("- 真实性：").append(!containsRiskWords(content) ? "✅ 通过" : "❌ 存在问题").append("\n");
        result.append("- 合法性：").append(content.length() > 10 ? "✅ 通过" : "❌ 内容过短").append("\n");
        result.append("- 规范性：").append("✅ 基本符合").append("\n");
        return result.toString();
    }
    
    private String checkGeneralCompliance(String content) {
        StringBuilder result = new StringBuilder();
        result.append("通用合规检查结果：\n");
        result.append("- 内容完整性：").append(content.length() > 50 ? "✅ 通过" : "❌ 内容过短").append("\n");
        result.append("- 语言规范性：").append("✅ 基本符合").append("\n");
        result.append("- 格式标准性：").append("✅ 符合要求").append("\n");
        return result.toString();
    }
}
