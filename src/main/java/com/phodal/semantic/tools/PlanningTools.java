package com.phodal.semantic.tools;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;

/**
 * 规划相关工具类
 */
public class PlanningTools {
    
    @Tool(description = "根据合约号执行订单的平仓、撤单、卖出操作")
    public String closeOrder(@ToolParam(description = "客户的平仓文本") String input) {
        if (input == null || input.trim().isEmpty()) {
            return "请提供平仓操作的具体信息。";
        }
        
        // 模拟平仓操作
        StringBuilder result = new StringBuilder();
        result.append("订单平仓操作执行结果：\n");
        result.append("==================\n\n");
        result.append("操作类型：平仓\n");
        result.append("操作内容：").append(input).append("\n");
        result.append("执行状态：已提交\n");
        result.append("执行时间：").append(java.time.LocalDateTime.now()).append("\n\n");
        
        // 简单解析平仓信息
        if (input.toLowerCase().contains("全部")) {
            result.append("操作详情：执行全部平仓操作\n");
        } else if (input.toLowerCase().contains("部分")) {
            result.append("操作详情：执行部分平仓操作\n");
        } else {
            result.append("操作详情：根据指定条件执行平仓\n");
        }
        
        result.append("风险提示：请确认平仓操作的准确性，操作不可撤销。\n");
        
        return result.toString();
    }
    
    @Tool(description = "根据订单信息查询订单价格")
    public String inquiryOrderPrice(@ToolParam(description = "客户的询价文本") String input) {
        if (input == null || input.trim().isEmpty()) {
            return "请提供询价的具体信息。";
        }
        
        // 模拟询价操作
        StringBuilder result = new StringBuilder();
        result.append("订单询价结果：\n");
        result.append("==================\n\n");
        result.append("询价内容：").append(input).append("\n");
        result.append("查询时间：").append(java.time.LocalDateTime.now()).append("\n\n");
        
        // 简单解析询价信息
        String[] parts = input.split("[\\s,，]");
        for (String part : parts) {
            if (part.matches("\\d{6}")) { // 股票代码
                result.append("标的代码：").append(part).append("\n");
                result.append("当前价格：").append(String.format("%.2f", Math.random() * 100 + 50)).append(" 元\n");
            } else if (part.toLowerCase().contains("m")) { // 期限
                result.append("期限：").append(part).append("\n");
            } else if (part.contains("w") || part.contains("万")) { // 金额
                result.append("名义本金：").append(part).append("\n");
            }
        }
        
        result.append("\n价格信息：\n");
        result.append("买入价：").append(String.format("%.4f", Math.random() * 0.1 + 0.05)).append("\n");
        result.append("卖出价：").append(String.format("%.4f", Math.random() * 0.1 + 0.06)).append("\n");
        result.append("中间价：").append(String.format("%.4f", Math.random() * 0.1 + 0.055)).append("\n");
        
        result.append("\n备注：价格仅供参考，实际成交价格以市场实时报价为准。\n");
        
        return result.toString();
    }
    
    @Tool(description = "根据订单信息执行开仓、下单、行权操作")
    public String placeOrder(@ToolParam(description = "客户的下单文本") String input) {
        if (input == null || input.trim().isEmpty()) {
            return "请提供下单的具体信息。";
        }
        
        // 模拟下单操作
        StringBuilder result = new StringBuilder();
        result.append("订单下单操作结果：\n");
        result.append("==================\n\n");
        result.append("下单内容：").append(input).append("\n");
        result.append("订单编号：").append("ORD").append(System.currentTimeMillis() % 100000).append("\n");
        result.append("提交时间：").append(java.time.LocalDateTime.now()).append("\n");
        result.append("订单状态：已提交，等待确认\n\n");
        
        // 解析下单信息
        result.append("订单详情：\n");
        String[] parts = input.split("[\\s,，]");
        for (String part : parts) {
            if (part.matches("\\d{6}")) {
                result.append("- 标的代码：").append(part).append("\n");
            } else if (part.toLowerCase().contains("看涨") || part.toLowerCase().contains("call")) {
                result.append("- 期权类型：看涨期权\n");
            } else if (part.toLowerCase().contains("看跌") || part.toLowerCase().contains("put")) {
                result.append("- 期权类型：看跌期权\n");
            } else if (part.contains("m")) {
                result.append("- 期限：").append(part).append("\n");
            } else if (part.contains("w") || part.contains("万")) {
                result.append("- 名义本金：").append(part).append("\n");
            }
        }
        
        result.append("\n风险提示：\n");
        result.append("- 期权交易存在风险，可能导致本金损失\n");
        result.append("- 请确保充分理解产品特性和风险\n");
        result.append("- 建议根据自身风险承受能力进行投资\n");
        
        return result.toString();
    }
    
    @Tool(description = "执行订单议价操作")
    public String bargainingOrder(@ToolParam(description = "客户的议价文本") String input) {
        if (input == null || input.trim().isEmpty()) {
            return "请提供议价的具体信息。";
        }
        
        // 模拟议价操作
        StringBuilder result = new StringBuilder();
        result.append("订单议价结果：\n");
        result.append("==================\n\n");
        result.append("议价内容：").append(input).append("\n");
        result.append("议价时间：").append(java.time.LocalDateTime.now()).append("\n\n");
        
        // 检查是否缺少必要信息
        if (!input.matches(".*\\d{6}.*")) {
            result.append("状态：议价失败\n");
            result.append("原因：订单缺失`标的代码`要素，请补充完全后重新询价。\n");
            return result.toString();
        }
        
        result.append("状态：议价成功\n");
        result.append("议价结果：\n");
        
        // 解析议价信息
        String[] parts = input.split("[\\s,，]");
        for (String part : parts) {
            if (part.matches("\\d{6}")) {
                result.append("- 标的代码：").append(part).append("\n");
            } else if (part.matches("\\d+\\.\\d+")) {
                result.append("- 议价价格：").append(part).append("\n");
                result.append("- 市场价格：").append(String.format("%.2f", Double.parseDouble(part) * 1.02)).append("\n");
                result.append("- 价格差异：").append(String.format("%.2f%%", 2.0)).append("\n");
            }
        }
        
        result.append("\n议价建议：\n");
        result.append("- 当前议价在合理范围内\n");
        result.append("- 建议关注市场波动情况\n");
        result.append("- 可考虑适当调整议价策略\n");
        
        return result.toString();
    }
    
    @Tool(description = "发送邮件给指定的收件人")
    public String emailSend(
            @ToolParam(description = "要发送的邮件正文。如果历史对话中已经生成了邮件内容且用户要求，则直接提取历史对话中的邮件内容") String input,
            @ToolParam(description = "附件列表", required = false) String attach,
            @ToolParam(description = "收件人列表") String to,
            @ToolParam(description = "寄件人") String sender,
            @ToolParam(description = "邮件标题") String title) {
        
        if (input == null || input.trim().isEmpty()) {
            return "请提供邮件正文内容。";
        }
        
        if (to == null || to.trim().isEmpty()) {
            return "请提供收件人信息。";
        }
        
        // 模拟邮件发送
        StringBuilder result = new StringBuilder();
        result.append("邮件发送结果：\n");
        result.append("==================\n\n");
        result.append("发送状态：成功\n");
        result.append("发送时间：").append(java.time.LocalDateTime.now()).append("\n\n");
        
        result.append("邮件详情：\n");
        result.append("发件人：").append(sender != null ? sender : "系统默认").append("\n");
        result.append("收件人：").append(to).append("\n");
        result.append("邮件标题：").append(title != null ? title : "无标题").append("\n");
        
        if (attach != null && !attach.trim().isEmpty()) {
            result.append("附件：").append(attach).append("\n");
        }
        
        result.append("\n邮件正文：\n");
        result.append("---\n");
        result.append(input);
        result.append("\n---\n");
        
        result.append("\n邮件已成功发送到指定收件人。\n");
        
        return result.toString();
    }
}
