# Semantic Migration Framework

这是一个从 Microsoft Semantic Kernel 迁移到 Spring AI 的框架雏形，提供了动态模型管理、工具加载和规划服务等核心功能。

## 核心组件

### 1. DynamicModelService
动态模型服务，支持根据不同业务配置创建对应的 ChatClient 实例。

**特性：**
- 支持多种模型（DeepSeek、OpenAI 等）
- 动态配置加载
- 缓存机制
- 业务特定配置

**配置文件：**
- `llm_config.json` - 默认 LLM 配置
- `{business}/config.json` - 业务特定配置

### 2. ToolLoader
工具加载器，负责动态加载和管理工具函数。

**支持的工具类型：**
- 通用工具（GeneralTools）
- 问答工具（QATools）
- 合规审核工具（ComplianceTools）
- 规划工具（PlanningTools）

### 3. PlannerService
规划服务，集成模型服务和工具加载器，处理用户请求。

**功能：**
- 使用默认的 `plan/skprompt.txt` 作为提示词
- 支持业务特定的工具和配置
- 自定义提示词支持

### 4. ConfigurationManager
配置管理器，负责加载和管理各种配置文件。

**管理的配置：**
- LLM 配置
- 业务配置
- 提示词模板

## 使用方法

### 1. 基本规划请求

```bash
curl -X POST http://localhost:8080/api/semantic/plan \
  -H "Content-Type: application/json" \
  -d '{
    "input": "外代销产品,请问给代销机构下发数据一般是几点?",
    "businessType": "qa"
  }'
```

### 2. 自定义提示词规划

```bash
curl -X POST http://localhost:8080/api/semantic/plan/custom \
  -H "Content-Type: application/json" \
  -d '{
    "input": "用户输入",
    "businessType": "compliance_review",
    "systemPrompt": "你是一个专业的合规审核助手..."
  }'
```

### 3. 获取可用工具

```bash
curl "http://localhost:8080/api/semantic/tools?businessType=qa"
```

### 4. 系统状态检查

```bash
curl http://localhost:8080/api/semantic/status
```

### 5. 健康检查

```bash
curl http://localhost:8080/api/semantic/health
```

## 配置文件结构

### LLM 配置 (`llm_config.json`)
```json
{
  "chat_service_llm": [
    {
      "base_url": "https://api.deepseek.com/",
      "model_name": "deepseek-chat",
      "api_key": "your-api-key"
    }
  ]
}
```

### 业务配置 (`{business}/config.json`)
```json
{
  "schema": 1,
  "type": "completion",
  "description": "业务描述",
  "execution_settings": {
    "default": {
      "ai_model_id": "llm-chat-8X7b",
      "max_tokens": 4000,
      "temperature": 0.1,
      "top_p": 0.1,
      "presence_penalty": 0.0,
      "frequency_penalty": 0.0
    }
  },
  "input_variables": [
    {
      "name": "variable_name",
      "description": "变量描述",
      "default": "",
      "is_required": true
    }
  ]
}
```

### 提示词模板 (`{business}/skprompt.txt`)
```text
你是一个智能助手，会根据用户最新的需求，选择最合适的函数来回答问题。
...
```

## 支持的业务类型

1. **plan** - 规划业务（默认）
2. **qa** / **tuoguan** - 问答业务
3. **compliance_review** - 合规审核业务

## 扩展指南

### 添加新的工具类

1. 创建工具类，使用 `@Tool` 注解标记方法
2. 在 `ToolLoader` 中注册新的工具类
3. 重新启动应用或调用重新加载接口

### 添加新的业务类型

1. 在 `resources` 下创建业务目录
2. 添加 `config.json` 和 `skprompt.txt` 文件
3. 在 `ToolLoader` 中添加业务特定的工具加载逻辑

### 自定义模型配置

1. 修改 `llm_config.json` 添加新的模型配置
2. 或在业务配置中指定特定的执行设置

## 注意事项

1. 确保 API Key 的安全性，不要提交到版本控制系统
2. 根据实际需求调整模型参数（temperature、max_tokens 等）
3. 定期检查系统健康状态和配置有效性
4. 工具函数应该处理异常情况并返回有意义的错误信息

## 依赖要求

- Spring Boot 3.4+
- Spring AI 1.0.1+
- Java 21+
- Jackson for JSON processing

## 开发和测试

框架提供了完整的 REST API 用于测试和集成：

- `/api/semantic/plan` - 执行规划
- `/api/semantic/tools` - 获取工具列表
- `/api/semantic/status` - 系统状态
- `/api/semantic/health` - 健康检查
- `/api/semantic/reload` - 重新加载配置

这个框架为从 Semantic Kernel 迁移到 Spring AI 提供了一个良好的起点，支持动态配置、工具管理和业务特定的定制。
